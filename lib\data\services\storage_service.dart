import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';
import '../models/property_model.dart';
import '../models/fuel_model.dart';

class StorageService {
  static StorageService? _instance;
  late SharedPreferences _prefs;

  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  StorageService._internal();

  static StorageService get instance {
    _instance ??= StorageService._internal();
    return _instance!;
  }

  Future<void> init() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      AppLogger.database('Storage service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize storage service', e);
      rethrow;
    }
  }

  // Secure Storage Methods (for sensitive data like tokens)
  Future<void> writeSecure(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
      AppLogger.database('Secure write', key);
    } catch (e) {
      AppLogger.error('Failed to write secure data', e);
      rethrow;
    }
  }

  Future<String?> readSecure(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      AppLogger.database('Secure read', key);
      return value;
    } catch (e) {
      AppLogger.error('Failed to read secure data', e);
      return null;
    }
  }

  Future<void> deleteSecure(String key) async {
    try {
      await _secureStorage.delete(key: key);
      AppLogger.database('Secure delete', key);
    } catch (e) {
      AppLogger.error('Failed to delete secure data', e);
    }
  }

  Future<void> clearSecure() async {
    try {
      await _secureStorage.deleteAll();
      AppLogger.database('Secure storage cleared');
    } catch (e) {
      AppLogger.error('Failed to clear secure storage', e);
    }
  }

  // SharedPreferences Methods (for app settings)
  Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
    AppLogger.database('SharedPrefs set string', key);
  }

  String? getString(String key) {
    return _prefs.getString(key);
  }

  Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
    AppLogger.database('SharedPrefs set bool', key);
  }

  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
    AppLogger.database('SharedPrefs set int', key);
  }

  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  Future<void> remove(String key) async {
    await _prefs.remove(key);
    AppLogger.database('SharedPrefs remove', key);
  }

  // User Data Methods
  Future<void> saveUser(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs.setString('current_user', userJson);
    AppLogger.database('User saved', 'users', user.id);
  }

  UserModel? getCurrentUser() {
    final userJson = _prefs.getString('current_user');
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userMap);
      } catch (e) {
        AppLogger.error('Failed to parse user data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> clearUser() async {
    await _prefs.remove('current_user');
    AppLogger.database('User cleared');
  }

  // Property Data Methods (simplified for now)
  Future<void> saveProperties(List<PropertyModel> properties) async {
    final propertiesJson = jsonEncode(properties.map((p) => p.toJson()).toList());
    await _prefs.setString('properties', propertiesJson);
    AppLogger.database('Properties saved', 'properties', properties.length.toString());
  }

  Future<void> saveProperty(PropertyModel property) async {
    // For now, just save to a simple key-value store
    final propertyJson = jsonEncode(property.toJson());
    await _prefs.setString('property_${property.id}', propertyJson);
    AppLogger.database('Property saved', 'properties', property.id);
  }

  List<PropertyModel> getProperties() {
    final propertiesJson = _prefs.getString('properties');
    if (propertiesJson != null) {
      try {
        final propertiesList = jsonDecode(propertiesJson) as List<dynamic>;
        return propertiesList
            .map((p) => PropertyModel.fromJson(p as Map<String, dynamic>))
            .toList();
      } catch (e) {
        AppLogger.error('Failed to parse properties data', e);
        return [];
      }
    }
    return [];
  }

  PropertyModel? getProperty(String id) {
    final propertyJson = _prefs.getString('property_$id');
    if (propertyJson != null) {
      try {
        final propertyMap = jsonDecode(propertyJson) as Map<String, dynamic>;
        return PropertyModel.fromJson(propertyMap);
      } catch (e) {
        AppLogger.error('Failed to parse property data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteProperty(String id) async {
    await _prefs.remove('property_$id');
    AppLogger.database('Property deleted', 'properties', id);
  }

  Future<void> clearProperties() async {
    await _prefs.remove('properties');
    // Also clear individual property entries
    final keys = _prefs.getKeys().where((key) => key.startsWith('property_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Properties cleared');
  }

  // Fuel Data Methods (simplified)
  Future<void> saveFuelRecords(List<FuelModel> fuelRecords) async {
    final fuelJson = jsonEncode(fuelRecords.map((f) => f.toJson()).toList());
    await _prefs.setString('fuel_records', fuelJson);
    AppLogger.database('Fuel records saved', 'fuel', fuelRecords.length.toString());
  }

  Future<void> saveFuelRecord(FuelModel fuel) async {
    final fuelJson = jsonEncode(fuel.toJson());
    await _prefs.setString('fuel_${fuel.id}', fuelJson);
    AppLogger.database('Fuel record saved', 'fuel', fuel.id);
  }

  List<FuelModel> getFuelRecords({String? propertyId}) {
    final fuelJson = _prefs.getString('fuel_records');
    if (fuelJson != null) {
      try {
        final fuelList = jsonDecode(fuelJson) as List<dynamic>;
        final allRecords = fuelList
            .map((f) => FuelModel.fromJson(f as Map<String, dynamic>))
            .toList();

        if (propertyId != null) {
          return allRecords.where((fuel) => fuel.propertyId == propertyId).toList();
        }
        return allRecords;
      } catch (e) {
        AppLogger.error('Failed to parse fuel records data', e);
        return [];
      }
    }
    return [];
  }

  FuelModel? getFuelRecord(String id) {
    final fuelJson = _prefs.getString('fuel_$id');
    if (fuelJson != null) {
      try {
        final fuelMap = jsonDecode(fuelJson) as Map<String, dynamic>;
        return FuelModel.fromJson(fuelMap);
      } catch (e) {
        AppLogger.error('Failed to parse fuel record data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteFuelRecord(String id) async {
    await _prefs.remove('fuel_$id');
    AppLogger.database('Fuel record deleted', 'fuel', id);
  }

  Future<void> clearFuelRecords() async {
    await _prefs.remove('fuel_records');
    final keys = _prefs.getKeys().where((key) => key.startsWith('fuel_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Fuel records cleared');
  }

  // Settings Methods
  Future<void> saveSetting(String key, Map<String, dynamic> value) async {
    final settingJson = jsonEncode(value);
    await _prefs.setString('setting_$key', settingJson);
    AppLogger.database('Setting saved', 'settings', key);
  }

  Map<String, dynamic>? getSetting(String key) {
    final settingJson = _prefs.getString('setting_$key');
    if (settingJson != null) {
      try {
        return jsonDecode(settingJson) as Map<String, dynamic>;
      } catch (e) {
        AppLogger.error('Failed to parse setting data', e);
        return null;
      }
    }
    return null;
  }

  Future<void> deleteSetting(String key) async {
    await _prefs.remove('setting_$key');
    AppLogger.database('Setting deleted', 'settings', key);
  }

  // Cache Management
  Future<void> clearCache() async {
    await clearProperties();
    await clearFuelRecords();
    final keys = _prefs.getKeys().where((key) => key.startsWith('setting_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
    AppLogger.database('Cache cleared');
  }

  Future<void> clearAllData() async {
    await clearUser();
    await clearCache();
    await _prefs.clear();
    await _secureStorage.deleteAll();
    AppLogger.database('All data cleared');
  }

  // Database size and maintenance
  int get cacheSize {
    return _prefs.getKeys().length;
  }

  Future<void> compactDatabase() async {
    // SharedPreferences doesn't need compacting
    AppLogger.database('Database compacted (no-op for SharedPreferences)');
  }

  void dispose() {
    // SharedPreferences doesn't need disposal
  }
}
