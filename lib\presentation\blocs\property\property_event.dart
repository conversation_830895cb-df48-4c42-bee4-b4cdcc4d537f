import 'package:equatable/equatable.dart';

abstract class PropertyEvent extends Equatable {
  const PropertyEvent();

  @override
  List<Object?> get props => [];
}

class PropertyLoadRequested extends PropertyEvent {
  final bool forceRefresh;

  const PropertyLoadRequested({this.forceRefresh = false});

  @override
  List<Object?> get props => [forceRefresh];
}

class PropertySearchRequested extends PropertyEvent {
  final String query;

  const PropertySearchRequested({required this.query});

  @override
  List<Object?> get props => [query];
}

class PropertyFilterChanged extends PropertyEvent {
  final String? status;
  final String? type;

  const PropertyFilterChanged({this.status, this.type});

  @override
  List<Object?> get props => [status, type];
}

class PropertyDetailsRequested extends PropertyEvent {
  final String propertyId;

  const PropertyDetailsRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class PropertyStatusRequested extends PropertyEvent {
  final String propertyId;

  const PropertyStatusRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class PropertyUpdateRequested extends PropertyEvent {
  final String propertyId;
  final Map<String, dynamic> data;

  const PropertyUpdateRequested({
    required this.propertyId,
    required this.data,
  });

  @override
  List<Object?> get props => [propertyId, data];
}

class PropertyCreateRequested extends PropertyEvent {
  final Map<String, dynamic> data;

  const PropertyCreateRequested({required this.data});

  @override
  List<Object?> get props => [data];
}

class PropertyDeleteRequested extends PropertyEvent {
  final String propertyId;

  const PropertyDeleteRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class PropertyRefreshRequested extends PropertyEvent {
  final String propertyId;

  const PropertyRefreshRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class PropertyCacheCleared extends PropertyEvent {
  const PropertyCacheCleared();
}

class PropertySyncRequested extends PropertyEvent {
  const PropertySyncRequested();
}

class PropertyOfflineDataLoaded extends PropertyEvent {
  const PropertyOfflineDataLoaded();
}
