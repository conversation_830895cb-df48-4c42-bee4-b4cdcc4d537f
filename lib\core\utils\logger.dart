import 'package:logger/logger.dart';
import '../config/app_config.dart';

class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.enableLogging) {
      if (error != null || stackTrace != null) {
        _logger.d(message, error: error, stackTrace: stackTrace);
      } else {
        _logger.d(message);
      }
    }
  }

  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.enableLogging) {
      if (error != null || stackTrace != null) {
        _logger.i(message, error: error, stackTrace: stackTrace);
      } else {
        _logger.i(message);
      }
    }
  }

  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.enableLogging) {
      if (error != null || stackTrace != null) {
        _logger.w(message, error: error, stackTrace: stackTrace);
      } else {
        _logger.w(message);
      }
    }
  }

  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.enableLogging) {
      if (error != null || stackTrace != null) {
        _logger.e(message, error: error, stackTrace: stackTrace);
      } else {
        _logger.e(message);
      }
    }
  }

  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.enableLogging) {
      if (error != null || stackTrace != null) {
        _logger.f(message, error: error, stackTrace: stackTrace);
      } else {
        _logger.f(message);
      }
    }
  }

  // API specific logging
  static void apiRequest(String method, String url, [Map<String, dynamic>? data]) {
    if (AppConfig.enableLogging) {
      if (data != null) {
        _logger.i('🌐 API Request: $method $url', error: data);
      } else {
        _logger.i('🌐 API Request: $method $url');
      }
    }
  }

  static void apiResponse(String method, String url, int statusCode, [dynamic data]) {
    if (AppConfig.enableLogging) {
      final emoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌';
      if (data != null) {
        _logger.i('$emoji API Response: $method $url [$statusCode]', error: data);
      } else {
        _logger.i('$emoji API Response: $method $url [$statusCode]');
      }
    }
  }

  static void apiError(String method, String url, dynamic error) {
    if (AppConfig.enableLogging) {
      _logger.e('💥 API Error: $method $url', error: error);
    }
  }

  // Navigation logging
  static void navigation(String from, String to) {
    if (AppConfig.enableLogging) {
      _logger.d('🧭 Navigation: $from → $to');
    }
  }

  // State management logging
  static void stateChange(String bloc, String event, String state) {
    if (AppConfig.enableLogging) {
      _logger.d('🔄 State Change: $bloc | Event: $event | State: $state');
    }
  }

  // Database operations
  static void database(String operation, [String? table, dynamic data]) {
    if (AppConfig.enableLogging) {
      final message = table != null ? '💾 Database: $operation on $table' : '💾 Database: $operation';
      if (data != null) {
        _logger.d(message, error: data);
      } else {
        _logger.d(message);
      }
    }
  }

  // Authentication logging
  static void auth(String action, [String? userId]) {
    if (AppConfig.enableLogging) {
      _logger.i('🔐 Auth: $action ${userId != null ? 'for user $userId' : ''}');
    }
  }

  // Performance logging
  static void performance(String operation, Duration duration) {
    if (AppConfig.enableLogging) {
      _logger.d('⚡ Performance: $operation took ${duration.inMilliseconds}ms');
    }
  }
}
