enum Environment { development, staging, production }

class AppConfig {
  static Environment _environment = Environment.development;

  static Environment get environment => _environment;

  static void setEnvironment(Environment env) {
    _environment = env;
  }

  static String get baseUrl {
    switch (_environment) {
      case Environment.development:
        return 'http://192.168.1.79:3000/api';
      case Environment.staging:
        return 'https://staging-api.propmanagement.com/api';
      case Environment.production:
        return 'https://api.propmanagement.com/api';
    }
  }

  static String get appName {
    switch (_environment) {
      case Environment.development:
        return 'PropMgmt Dev';
      case Environment.staging:
        return 'PropMgmt Staging';
      case Environment.production:
        return 'Property Management';
    }
  }

  static bool get isDebug {
    return _environment == Environment.development;
  }

  static bool get enableLogging {
    return _environment != Environment.production;
  }

  static String get firebaseProjectId {
    switch (_environment) {
      case Environment.development:
        return 'propmanagement-dev';
      case Environment.staging:
        return 'propmanagement-staging';
      case Environment.production:
        return 'propmanagement-prod';
    }
  }

  static Map<String, dynamic> get dioConfig {
    return {
      'connectTimeout': 30000,
      'receiveTimeout': 30000,
      'sendTimeout': 30000,
      'enableLogging': enableLogging,
    };
  }
}
