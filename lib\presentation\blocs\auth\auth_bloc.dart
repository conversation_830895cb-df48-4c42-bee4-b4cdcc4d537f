import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

import '../../../core/utils/logger.dart';
import '../../../core/constants/app_constants.dart';
import '../../../data/models/user_model.dart';
import '../../../data/services/api_service.dart';
import '../../../data/services/storage_service.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final ApiService _apiService;
  final StorageService _storageService;

  AuthBloc({
    required ApiService apiService,
    required StorageService storageService,
  })  : _apiService = apiService,
        _storageService = storageService,
        super(const AuthInitial()) {
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthLoginRequested>(_onAuthLoginRequested);
    on<AuthRegisterRequested>(_onAuthRegisterRequested);
    on<AuthLogoutRequested>(_onAuthLogoutRequested);
    on<AuthTokenRefreshRequested>(_onAuthTokenRefreshRequested);
    on<AuthUserUpdated>(_onAuthUserUpdated);
    on<AuthPasswordChangeRequested>(_onAuthPasswordChangeRequested);
    on<AuthForgotPasswordRequested>(_onAuthForgotPasswordRequested);
    on<AuthResetPasswordRequested>(_onAuthResetPasswordRequested);
    on<AuthBiometricLoginRequested>(_onAuthBiometricLoginRequested);
    on<AuthBiometricSetupRequested>(_onAuthBiometricSetupRequested);
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.auth('Checking authentication status');
      emit(const AuthLoading());

      // Check for stored user and token
      final user = _storageService.getCurrentUser();
      final token = await _storageService.readSecure(AppConstants.accessTokenKey);

      if (user != null && token != null) {
        // Check if token is expired
        if (JwtDecoder.isExpired(token)) {
          AppLogger.auth('Token expired, attempting refresh');
          add(const AuthTokenRefreshRequested());
          return;
        }

        // Verify token with server
        try {
          final currentUser = await _apiService.getCurrentUser();
          await _storageService.saveUser(currentUser);
          
          final rememberMe = _storageService.getBool('remember_me') ?? false;
          final biometricEnabled = _storageService.getBool('biometric_enabled') ?? false;

          emit(AuthAuthenticated(
            user: currentUser,
            rememberMe: rememberMe,
            biometricEnabled: biometricEnabled,
          ));
          AppLogger.auth('Authentication verified', currentUser.id);
        } catch (e) {
          AppLogger.auth('Token verification failed, logging out');
          add(const AuthLogoutRequested());
        }
      } else {
        final isFirstTime = _storageService.getBool('first_time') ?? true;
        emit(AuthUnauthenticated(isFirstTime: isFirstTime));
        AppLogger.auth('No valid authentication found');
      }
    } catch (e) {
      AppLogger.error('Auth check failed', e);
      emit(AuthError(errorMessage: 'Authentication check failed: ${e.toString()}'));
    }
  }

  Future<void> _onAuthLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.auth('Login attempt for ${event.email}');
      emit(const AuthLoginLoading());

      final loginRequest = LoginRequest(
        email: event.email,
        password: event.password,
        rememberMe: event.rememberMe,
      );

      final authResponse = await _apiService.login(loginRequest);

      // Store tokens securely
      await _storageService.writeSecure(
        AppConstants.accessTokenKey,
        authResponse.accessToken,
      );
      await _storageService.writeSecure(
        AppConstants.refreshTokenKey,
        authResponse.refreshToken,
      );

      // Store user data
      await _storageService.saveUser(authResponse.user);
      
      // Store preferences
      await _storageService.setBool('remember_me', event.rememberMe);
      await _storageService.setBool('first_time', false);

      emit(AuthAuthenticated(
        user: authResponse.user,
        rememberMe: event.rememberMe,
      ));

      AppLogger.auth('Login successful', authResponse.user.id);
    } catch (e) {
      AppLogger.error('Login failed', e);
      emit(AuthError(errorMessage: _getErrorMessage(e)));
    }
  }

  Future<void> _onAuthRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.auth('Registration attempt for ${event.email}');
      emit(const AuthRegisterLoading());

      final registerData = {
        'name': event.name,
        'email': event.email,
        'password': event.password,
        if (event.phone != null) 'phone': event.phone,
      };

      final authResponse = await _apiService.register(registerData);

      // Store tokens securely
      await _storageService.writeSecure(
        AppConstants.accessTokenKey,
        authResponse.accessToken,
      );
      await _storageService.writeSecure(
        AppConstants.refreshTokenKey,
        authResponse.refreshToken,
      );

      // Store user data
      await _storageService.saveUser(authResponse.user);
      await _storageService.setBool('first_time', false);

      emit(AuthAuthenticated(user: authResponse.user));
      AppLogger.auth('Registration successful', authResponse.user.id);
    } catch (e) {
      AppLogger.error('Registration failed', e);
      emit(AuthError(errorMessage: _getErrorMessage(e)));
    }
  }

  Future<void> _onAuthLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.auth('Logout requested');
      emit(const AuthLoading());

      // Call logout API
      try {
        await _apiService.logout();
      } catch (e) {
        // Continue with logout even if API call fails
        AppLogger.warning('Logout API call failed', e);
      }

      // Clear all stored data
      await _storageService.clearUser();
      await _storageService.deleteSecure(AppConstants.accessTokenKey);
      await _storageService.deleteSecure(AppConstants.refreshTokenKey);
      
      // Keep remember me and biometric settings
      // await _storageService.remove('remember_me');
      // await _storageService.remove('biometric_enabled');

      emit(const AuthUnauthenticated());
      AppLogger.auth('Logout completed');
    } catch (e) {
      AppLogger.error('Logout failed', e);
      emit(AuthError(errorMessage: 'Logout failed: ${e.toString()}'));
    }
  }

  Future<void> _onAuthTokenRefreshRequested(
    AuthTokenRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.auth('Token refresh requested');
      
      final refreshToken = await _storageService.readSecure(AppConstants.refreshTokenKey);
      if (refreshToken == null) {
        emit(const AuthUnauthenticated());
        return;
      }

      final authResponse = await _apiService.refreshToken({
        'refreshToken': refreshToken,
      });

      // Update stored tokens
      await _storageService.writeSecure(
        AppConstants.accessTokenKey,
        authResponse.accessToken,
      );
      await _storageService.writeSecure(
        AppConstants.refreshTokenKey,
        authResponse.refreshToken,
      );

      // Update user data
      await _storageService.saveUser(authResponse.user);

      final rememberMe = _storageService.getBool('remember_me') ?? false;
      final biometricEnabled = _storageService.getBool('biometric_enabled') ?? false;

      emit(AuthAuthenticated(
        user: authResponse.user,
        rememberMe: rememberMe,
        biometricEnabled: biometricEnabled,
      ));

      AppLogger.auth('Token refresh successful');
    } catch (e) {
      AppLogger.error('Token refresh failed', e);
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onAuthUserUpdated(
    AuthUserUpdated event,
    Emitter<AuthState> emit,
  ) async {
    try {
      if (state.user == null) return;

      final updatedUser = await _apiService.updateUser(
        state.user!.id,
        event.userData,
      );

      await _storageService.saveUser(updatedUser);

      emit(state.copyWith(user: updatedUser));
      AppLogger.auth('User updated', updatedUser.id);
    } catch (e) {
      AppLogger.error('User update failed', e);
      emit(AuthError(
        errorMessage: 'Failed to update user: ${e.toString()}',
        user: state.user,
      ));
    }
  }

  Future<void> _onAuthPasswordChangeRequested(
    AuthPasswordChangeRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthPasswordChangeLoading(user: state.user));

      // Implementation depends on your API
      // await _apiService.changePassword({
      //   'currentPassword': event.currentPassword,
      //   'newPassword': event.newPassword,
      // });

      emit(state.copyWith(status: AuthStatus.authenticated));
      AppLogger.auth('Password changed successfully');
    } catch (e) {
      AppLogger.error('Password change failed', e);
      emit(AuthError(
        errorMessage: 'Failed to change password: ${e.toString()}',
        user: state.user,
      ));
    }
  }

  Future<void> _onAuthForgotPasswordRequested(
    AuthForgotPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthForgotPasswordLoading());

      // Implementation depends on your API
      // await _apiService.forgotPassword({'email': event.email});

      emit(const AuthUnauthenticated());
      AppLogger.auth('Password reset email sent to ${event.email}');
    } catch (e) {
      AppLogger.error('Forgot password failed', e);
      emit(AuthError(errorMessage: 'Failed to send reset email: ${e.toString()}'));
    }
  }

  Future<void> _onAuthResetPasswordRequested(
    AuthResetPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());

      // Implementation depends on your API
      // await _apiService.resetPassword({
      //   'token': event.token,
      //   'password': event.newPassword,
      // });

      emit(const AuthUnauthenticated());
      AppLogger.auth('Password reset successful');
    } catch (e) {
      AppLogger.error('Password reset failed', e);
      emit(AuthError(errorMessage: 'Failed to reset password: ${e.toString()}'));
    }
  }

  Future<void> _onAuthBiometricLoginRequested(
    AuthBiometricLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      // Implementation for biometric authentication
      // This would typically involve local authentication
      AppLogger.auth('Biometric login requested');
      
      // For now, just check if we have stored credentials
      add(const AuthCheckRequested());
    } catch (e) {
      AppLogger.error('Biometric login failed', e);
      emit(AuthError(errorMessage: 'Biometric authentication failed'));
    }
  }

  Future<void> _onAuthBiometricSetupRequested(
    AuthBiometricSetupRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthBiometricSetupLoading(user: state.user));

      await _storageService.setBool('biometric_enabled', event.enable);

      emit(state.copyWith(
        status: AuthStatus.authenticated,
        biometricEnabled: event.enable,
      ));

      AppLogger.auth('Biometric ${event.enable ? 'enabled' : 'disabled'}');
    } catch (e) {
      AppLogger.error('Biometric setup failed', e);
      emit(AuthError(
        errorMessage: 'Failed to setup biometric authentication',
        user: state.user,
      ));
    }
  }

  String _getErrorMessage(dynamic error) {
    // Parse error message from API response
    if (error.toString().contains('401')) {
      return 'Invalid email or password';
    } else if (error.toString().contains('422')) {
      return 'Please check your input and try again';
    } else if (error.toString().contains('500')) {
      return 'Server error. Please try again later';
    } else if (error.toString().contains('network')) {
      return 'Network error. Please check your connection';
    }
    return 'An unexpected error occurred';
  }
}
