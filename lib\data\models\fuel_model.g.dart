// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fuel_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FuelModel _$FuelModelFromJson(Map<String, dynamic> json) => FuelModel(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      currentLevel: (json['currentLevel'] as num).toDouble(),
      previousLevel: (json['previousLevel'] as num?)?.toDouble(),
      entryType: $enumDecode(_$FuelEntryTypeEnumMap, json['entryType']),
      addedAmount: (json['addedAmount'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      recordedBy: json['recordedBy'] as String,
      recordedAt: DateTime.parse(json['recordedAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$FuelModelToJson(FuelModel instance) => <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'currentLevel': instance.currentLevel,
      'previousLevel': instance.previousLevel,
      'entryType': _$FuelEntryTypeEnumMap[instance.entryType]!,
      'addedAmount': instance.addedAmount,
      'notes': instance.notes,
      'images': instance.images,
      'recordedBy': instance.recordedBy,
      'recordedAt': instance.recordedAt.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$FuelEntryTypeEnumMap = {
  FuelEntryType.reading: 'reading',
  FuelEntryType.addition: 'addition',
  FuelEntryType.consumption: 'consumption',
};

FuelCreateRequest _$FuelCreateRequestFromJson(Map<String, dynamic> json) =>
    FuelCreateRequest(
      propertyId: json['propertyId'] as String,
      currentLevel: (json['currentLevel'] as num).toDouble(),
      entryType: $enumDecode(_$FuelEntryTypeEnumMap, json['entryType']),
      addedAmount: (json['addedAmount'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      imageBase64: (json['imageBase64'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FuelCreateRequestToJson(FuelCreateRequest instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'currentLevel': instance.currentLevel,
      'entryType': _$FuelEntryTypeEnumMap[instance.entryType]!,
      'addedAmount': instance.addedAmount,
      'notes': instance.notes,
      'imageBase64': instance.imageBase64,
    };

FuelAnalytics _$FuelAnalyticsFromJson(Map<String, dynamic> json) =>
    FuelAnalytics(
      propertyId: json['propertyId'] as String,
      averageConsumption: (json['averageConsumption'] as num).toDouble(),
      totalConsumption: (json['totalConsumption'] as num).toDouble(),
      totalAdditions: (json['totalAdditions'] as num).toDouble(),
      totalReadings: (json['totalReadings'] as num).toInt(),
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      trends: (json['trends'] as List<dynamic>?)
          ?.map((e) => FuelTrend.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$FuelAnalyticsToJson(FuelAnalytics instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'averageConsumption': instance.averageConsumption,
      'totalConsumption': instance.totalConsumption,
      'totalAdditions': instance.totalAdditions,
      'totalReadings': instance.totalReadings,
      'periodStart': instance.periodStart.toIso8601String(),
      'periodEnd': instance.periodEnd.toIso8601String(),
      'trends': instance.trends,
    };

FuelTrend _$FuelTrendFromJson(Map<String, dynamic> json) => FuelTrend(
      date: DateTime.parse(json['date'] as String),
      level: (json['level'] as num).toDouble(),
      consumption: (json['consumption'] as num?)?.toDouble(),
      addition: (json['addition'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$FuelTrendToJson(FuelTrend instance) => <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'level': instance.level,
      'consumption': instance.consumption,
      'addition': instance.addition,
    };
