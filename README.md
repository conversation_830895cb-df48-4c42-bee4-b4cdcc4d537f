# Flutter Hello World App

This is a basic Flutter Hello World application scaffolded to run on an Android emulator.

## Getting Started

1. Make sure you have [Flutter](https://docs.flutter.dev/get-started/install) and [Android Studio/Emulator](https://developer.android.com/studio) installed and set up.
2. Open this project in VS Code.
3. To run the app on an Android emulator, use the command:

    ```
    flutter run
    ```

## Notes
- Your main app code is in `lib/main.dart`.
- If you see Java/Gradle compatibility warnings, follow the instructions in the terminal output above to resolve them.

---

# propmanagement

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
