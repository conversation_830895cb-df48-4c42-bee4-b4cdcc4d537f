import 'package:equatable/equatable.dart';
import '../../../data/models/user_model.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthState extends Equatable {
  final AuthStatus status;
  final UserModel? user;
  final String? errorMessage;
  final bool isLoading;
  final bool rememberMe;
  final bool biometricEnabled;
  final bool isFirstTime;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.errorMessage,
    this.isLoading = false,
    this.rememberMe = false,
    this.biometricEnabled = false,
    this.isFirstTime = true,
  });

  AuthState copyWith({
    AuthStatus? status,
    UserModel? user,
    String? errorMessage,
    bool? isLoading,
    bool? rememberMe,
    bool? biometricEnabled,
    bool? isFirstTime,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
      rememberMe: rememberMe ?? this.rememberMe,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      isFirstTime: isFirstTime ?? this.isFirstTime,
    );
  }

  bool get isAuthenticated => status == AuthStatus.authenticated && user != null;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;
  bool get hasError => status == AuthStatus.error && errorMessage != null;

  // Role-based access helpers
  bool get isAdmin => user?.role == UserRole.admin;
  bool get isManager => user?.role == UserRole.manager;
  bool get isFieldWorker => user?.role == UserRole.fieldWorker;
  bool get isSecurity => user?.role == UserRole.security;
  bool get isGuest => user?.role == UserRole.guest;

  bool get canManageUsers => isAdmin;
  bool get canManageProperties => isAdmin || isManager;
  bool get canViewAllProperties => isAdmin || isManager;
  bool get canCreateMaintenanceIssues => !isGuest;
  bool get canUpdateFuelLevels => isFieldWorker || isManager || isAdmin;
  bool get canViewAnalytics => isManager || isAdmin;
  bool get canManageSettings => isAdmin;

  bool canAccessProperty(String propertyId) {
    if (isAdmin || isManager) return true;
    return user?.canAccessProperty(propertyId) ?? false;
  }

  bool hasPermission(String permission) {
    return user?.hasPermission(permission) ?? false;
  }

  @override
  List<Object?> get props => [
        status,
        user,
        errorMessage,
        isLoading,
        rememberMe,
        biometricEnabled,
        isFirstTime,
      ];
}

// Specific state classes for different auth scenarios
class AuthInitial extends AuthState {
  const AuthInitial() : super(status: AuthStatus.initial);
}

class AuthLoading extends AuthState {
  const AuthLoading() : super(status: AuthStatus.loading, isLoading: true);
}

class AuthAuthenticated extends AuthState {
  const AuthAuthenticated({
    required UserModel user,
    bool rememberMe = false,
    bool biometricEnabled = false,
  }) : super(
          status: AuthStatus.authenticated,
          user: user,
          rememberMe: rememberMe,
          biometricEnabled: biometricEnabled,
        );
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated({
    String? errorMessage,
    bool isFirstTime = false,
  }) : super(
          status: AuthStatus.unauthenticated,
          errorMessage: errorMessage,
          isFirstTime: isFirstTime,
        );
}

class AuthError extends AuthState {
  const AuthError({
    required String errorMessage,
    UserModel? user,
  }) : super(
          status: AuthStatus.error,
          errorMessage: errorMessage,
          user: user,
        );
}

// Loading states for specific operations
class AuthLoginLoading extends AuthState {
  const AuthLoginLoading() : super(status: AuthStatus.loading, isLoading: true);
}

class AuthRegisterLoading extends AuthState {
  const AuthRegisterLoading() : super(status: AuthStatus.loading, isLoading: true);
}

class AuthPasswordChangeLoading extends AuthState {
  const AuthPasswordChangeLoading({UserModel? user})
      : super(status: AuthStatus.loading, isLoading: true, user: user);
}

class AuthForgotPasswordLoading extends AuthState {
  const AuthForgotPasswordLoading()
      : super(status: AuthStatus.loading, isLoading: true);
}

class AuthBiometricSetupLoading extends AuthState {
  const AuthBiometricSetupLoading({UserModel? user})
      : super(status: AuthStatus.loading, isLoading: true, user: user);
}
