import 'dart:convert';
import 'dart:math';

import '../models/user_model.dart';
import '../models/property_model.dart';
import '../models/fuel_model.dart';
import 'api_service.dart';

class MockApiService implements ApiService {
  // Demo credentials
  static const Map<String, Map<String, dynamic>> _demoUsers = {
    '<EMAIL>': {
      'password': 'admin123',
      'user': {
        'id': 'admin-001',
        'name': 'Admin User',
        'email': '<EMAIL>',
        'role': 'admin',
        'phone': '+1234567890',
        'avatar': null,
        'isActive': true,
        'lastLoginAt': '2024-01-15T10:30:00Z',
        'createdAt': '2024-01-01T00:00:00Z',
        'updatedAt': '2024-01-15T10:30:00Z',
        'permissions': ['all'],
        'assignedProperties': [],
      }
    },
    '<EMAIL>': {
      'password': 'manager123',
      'user': {
        'id': 'manager-001',
        'name': 'Property Manager',
        'email': '<EMAIL>',
        'role': 'manager',
        'phone': '+1234567891',
        'avatar': null,
        'isActive': true,
        'lastLoginAt': '2024-01-15T09:15:00Z',
        'createdAt': '2024-01-01T00:00:00Z',
        'updatedAt': '2024-01-15T09:15:00Z',
        'permissions': ['manage_properties', 'view_analytics'],
        'assignedProperties': ['prop-001', 'prop-002'],
      }
    },
    '<EMAIL>': {
      'password': 'worker123',
      'user': {
        'id': 'worker-001',
        'name': 'Field Worker',
        'email': '<EMAIL>',
        'role': 'fieldWorker',
        'phone': '+1234567892',
        'avatar': null,
        'isActive': true,
        'lastLoginAt': '2024-01-15T08:45:00Z',
        'createdAt': '2024-01-01T00:00:00Z',
        'updatedAt': '2024-01-15T08:45:00Z',
        'permissions': ['update_fuel', 'create_maintenance'],
        'assignedProperties': ['prop-001'],
      }
    },
  };

  // Mock JWT token
  String _generateMockToken(String userId) {
    final header = base64Encode(utf8.encode('{"alg":"HS256","typ":"JWT"}'));
    final payload = base64Encode(utf8.encode(jsonEncode({
      'sub': userId,
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'exp': DateTime.now().add(Duration(hours: 24)).millisecondsSinceEpoch ~/ 1000,
    })));
    final signature = base64Encode(utf8.encode('mock-signature'));
    return '$header.$payload.$signature';
  }

  @override
  Future<AuthResponse> login(LoginRequest request) async {
    // Simulate network delay
    await Future.delayed(Duration(milliseconds: 800));

    final userCredentials = _demoUsers[request.email];
    if (userCredentials == null || userCredentials['password'] != request.password) {
      throw Exception('Invalid email or password');
    }

    final userData = userCredentials['user'] as Map<String, dynamic>;
    final user = UserModel.fromJson(userData);
    final accessToken = _generateMockToken(user.id);
    final refreshToken = _generateMockToken('${user.id}-refresh');

    return AuthResponse(
      accessToken: accessToken,
      refreshToken: refreshToken,
      user: user,
      expiresAt: DateTime.now().add(Duration(hours: 24)),
    );
  }

  @override
  Future<AuthResponse> register(Map<String, dynamic> request) async {
    await Future.delayed(Duration(milliseconds: 1000));
    throw Exception('Registration not available in demo mode');
  }

  @override
  Future<AuthResponse> refreshToken(Map<String, dynamic> request) async {
    await Future.delayed(Duration(milliseconds: 500));

    // For demo, just return a new token
    final user = UserModel.fromJson(_demoUsers.values.first['user']);
    final accessToken = _generateMockToken(user.id);
    final refreshToken = _generateMockToken('${user.id}-refresh');

    return AuthResponse(
      accessToken: accessToken,
      refreshToken: refreshToken,
      user: user,
      expiresAt: DateTime.now().add(Duration(hours: 24)),
    );
  }

  @override
  Future<void> logout() async {
    await Future.delayed(Duration(milliseconds: 300));
    // Mock logout - no action needed
  }

  @override
  Future<UserModel> getCurrentUser() async {
    await Future.delayed(Duration(milliseconds: 400));
    // Return the first demo user for simplicity
    final userData = _demoUsers.values.first['user'] as Map<String, dynamic>;
    return UserModel.fromJson(userData);
  }

  @override
  Future<List<PropertyModel>> getProperties({
    int? page,
    int? limit,
    String? search,
    String? status,
    String? type,
  }) async {
    await Future.delayed(Duration(milliseconds: 600));

    // Return mock properties
    return _generateMockProperties();
  }

  List<PropertyModel> _generateMockProperties() {
    return [
      PropertyModel(
        id: 'prop-001',
        name: 'Downtown Office Complex',
        address: '123 Business Ave, Downtown',
        type: PropertyType.commercial,
        status: PropertyStatus.active,
        description: 'Modern office complex with 24/7 security',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime.now().subtract(Duration(days: 30)),
        updatedAt: DateTime.now().subtract(Duration(hours: 2)),
      ),
      PropertyModel(
        id: 'prop-002',
        name: 'Residential Tower A',
        address: '456 Residential St, Uptown',
        type: PropertyType.residential,
        status: PropertyStatus.active,
        description: 'Luxury residential tower with amenities',
        latitude: 40.7589,
        longitude: -73.9851,
        createdAt: DateTime.now().subtract(Duration(days: 45)),
        updatedAt: DateTime.now().subtract(Duration(hours: 5)),
      ),
      PropertyModel(
        id: 'prop-003',
        name: 'Industrial Warehouse',
        address: '789 Industrial Blvd, Industrial Zone',
        type: PropertyType.industrial,
        status: PropertyStatus.maintenance,
        description: 'Large warehouse facility for storage',
        latitude: 40.6892,
        longitude: -74.0445,
        createdAt: DateTime.now().subtract(Duration(days: 60)),
        updatedAt: DateTime.now().subtract(Duration(hours: 1)),
      ),
    ];
  }

  // Implement other required methods with mock responses
  @override
  Future<PropertyModel> getProperty(String id) async {
    await Future.delayed(Duration(milliseconds: 400));
    final properties = _generateMockProperties();
    return properties.firstWhere((p) => p.id == id, orElse: () => properties.first);
  }

  @override
  Future<PropertyMetrics> getPropertyStatus(String id) async {
    await Future.delayed(Duration(milliseconds: 500));
    final random = Random();
    return PropertyMetrics(
      fuelLevel: (65 + random.nextInt(30)).toDouble(),
      activeCameras: 8 + random.nextInt(4),
      totalCameras: 12,
      openMaintenanceIssues: random.nextInt(3),
      lastUpdated: DateTime.now().subtract(Duration(minutes: random.nextInt(60))),
    );
  }

  @override
  Future<PropertyModel> updateProperty(String id, Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 700));
    final properties = _generateMockProperties();
    return properties.firstWhere((p) => p.id == id, orElse: () => properties.first);
  }

  @override
  Future<List<FuelModel>> getFuelRecords(String propertyId, {int? page, int? limit, String? from, String? to}) async {
    await Future.delayed(Duration(milliseconds: 600));
    return []; // Return empty list for now
  }

  @override
  Future<FuelModel> createFuelRecord(FuelCreateRequest request) async {
    await Future.delayed(Duration(milliseconds: 500));
    return FuelModel(
      id: 'fuel-${DateTime.now().millisecondsSinceEpoch}',
      propertyId: request.propertyId,
      currentLevel: request.currentLevel,
      entryType: request.entryType,
      addedAmount: request.addedAmount,
      notes: request.notes,
      recordedBy: 'demo-user',
      recordedAt: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Future<FuelModel> updateFuelRecord(String id, Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 500));
    return FuelModel(
      id: id,
      propertyId: 'prop-001',
      currentLevel: data['currentLevel'] ?? 50.0,
      entryType: FuelEntryType.reading,
      recordedBy: 'demo-user',
      recordedAt: DateTime.now(),
      createdAt: DateTime.now().subtract(Duration(days: 1)),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Future<void> deleteFuelRecord(String id) async {
    await Future.delayed(Duration(milliseconds: 300));
    // Mock delete
  }

  @override
  Future<FuelAnalytics> getFuelAnalytics(String propertyId, {String? period, String? from, String? to}) async {
    await Future.delayed(Duration(milliseconds: 600));
    return FuelAnalytics(
      propertyId: propertyId,
      averageConsumption: 15.5,
      totalConsumption: 450.0,
      totalAdditions: 200.0,
      totalReadings: 30,
      periodStart: DateTime.now().subtract(Duration(days: 30)),
      periodEnd: DateTime.now(),
    );
  }

  // Add placeholder implementations for all other methods
  @override
  Future<dynamic> getMaintenanceIssues({String? propertyId, String? status, String? priority, int? page, int? limit}) async {
    await Future.delayed(Duration(milliseconds: 600));
    return [];
  }

  @override
  Future<dynamic> createMaintenanceIssue(Map<String, dynamic> request) async {
    await Future.delayed(Duration(milliseconds: 700));
    throw UnimplementedError('Mock implementation');
  }

  @override
  Future<dynamic> updateMaintenanceIssue(String id, Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 500));
    throw UnimplementedError('Mock implementation');
  }

  @override
  Future<void> deleteMaintenanceIssue(String id) async {
    await Future.delayed(Duration(milliseconds: 300));
    // Mock delete
  }

  @override
  Future<dynamic> getMaintenanceIssue(String id) async {
    await Future.delayed(Duration(milliseconds: 400));
    return {};
  }

  @override
  Future<dynamic> getAttendanceRecords(String siteId, {String? date, String? userId, int? page, int? limit}) async {
    await Future.delayed(Duration(milliseconds: 600));
    return [];
  }

  @override
  Future<dynamic> recordAttendance(Map<String, dynamic> request) async {
    await Future.delayed(Duration(milliseconds: 500));
    return {};
  }

  @override
  Future<dynamic> getAttendanceReports({String? propertyId, String? from, String? to, String? userId}) async {
    await Future.delayed(Duration(milliseconds: 700));
    return {};
  }

  @override
  Future<dynamic> getOttServices(String propertyId) async {
    await Future.delayed(Duration(milliseconds: 500));
    return [];
  }

  @override
  Future<dynamic> createOttService(Map<String, dynamic> request) async {
    await Future.delayed(Duration(milliseconds: 600));
    return {};
  }

  @override
  Future<dynamic> updateOttService(String id, Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 500));
    return {};
  }

  @override
  Future<void> deleteOttService(String id) async {
    await Future.delayed(Duration(milliseconds: 300));
    // Mock delete
  }

  @override
  Future<dynamic> getDashboardStatus({String? propertyId}) async {
    await Future.delayed(Duration(milliseconds: 500));
    return {
      'totalProperties': 3,
      'activeProperties': 2,
      'maintenanceIssues': 1,
      'fuelAlerts': 0,
    };
  }

  @override
  Future<dynamic> getDashboardAnalytics({String? propertyId, String? period}) async {
    await Future.delayed(Duration(milliseconds: 600));
    return {};
  }

  @override
  Future<List<UserModel>> getUsers({int? page, int? limit, String? role, String? search}) async {
    await Future.delayed(Duration(milliseconds: 600));
    return _demoUsers.values.map((u) => UserModel.fromJson(u['user'])).toList();
  }

  @override
  Future<UserModel> createUser(Map<String, dynamic> request) async {
    await Future.delayed(Duration(milliseconds: 700));
    throw UnimplementedError('Mock implementation');
  }

  @override
  Future<UserModel> updateUser(String id, Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 500));
    throw UnimplementedError('Mock implementation');
  }

  @override
  Future<void> deleteUser(String id) async {
    await Future.delayed(Duration(milliseconds: 300));
    // Mock delete
  }

  @override
  Future<dynamic> getThresholds() async {
    await Future.delayed(Duration(milliseconds: 400));
    return {};
  }

  @override
  Future<dynamic> updateThresholds(Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 500));
    return {};
  }

  @override
  Future<dynamic> uploadFile(List<int> file, String? type, String? propertyId) async {
    await Future.delayed(Duration(milliseconds: 1000));
    return {'url': 'https://example.com/mock-file.jpg'};
  }

  @override
  Future<dynamic> getNotifications({int? page, int? limit, bool? unread}) async {
    await Future.delayed(Duration(milliseconds: 500));
    return [];
  }

  @override
  Future<void> markNotificationAsRead(String id) async {
    await Future.delayed(Duration(milliseconds: 300));
    // Mock mark as read
  }

  @override
  Future<void> markAllNotificationsAsRead() async {
    await Future.delayed(Duration(milliseconds: 400));
    // Mock mark all as read
  }

  @override
  Future<dynamic> getSettings() async {
    await Future.delayed(Duration(milliseconds: 400));
    return {};
  }

  @override
  Future<dynamic> updateSettings(Map<String, dynamic> data) async {
    await Future.delayed(Duration(milliseconds: 500));
    return {};
  }
}
