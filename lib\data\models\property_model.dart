import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'property_model.g.dart';

enum PropertyStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('maintenance')
  maintenance,
  @JsonValue('critical')
  critical,
}

enum PropertyType {
  @JsonValue('residential')
  residential,
  @JsonValue('commercial')
  commercial,
  @JsonValue('industrial')
  industrial,
  @JsonValue('mixed')
  mixed,
}

@JsonSerializable()
class PropertyModel extends Equatable {
  final String id;
  final String name;
  final String address;
  final PropertyType type;
  final PropertyStatus status;
  final double? latitude;
  final double? longitude;
  final String? description;
  final List<String>? images;
  final String? managerId;
  final List<String>? assignedWorkers;
  final DateTime createdAt;
  final DateTime updatedAt;
  final PropertyMetrics? metrics;
  final Map<String, dynamic>? configuration;

  const PropertyModel({
    required this.id,
    required this.name,
    required this.address,
    required this.type,
    required this.status,
    this.latitude,
    this.longitude,
    this.description,
    this.images,
    this.managerId,
    this.assignedWorkers,
    required this.createdAt,
    required this.updatedAt,
    this.metrics,
    this.configuration,
  });

  factory PropertyModel.fromJson(Map<String, dynamic> json) =>
      _$PropertyModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyModelToJson(this);

  PropertyModel copyWith({
    String? id,
    String? name,
    String? address,
    PropertyType? type,
    PropertyStatus? status,
    double? latitude,
    double? longitude,
    String? description,
    List<String>? images,
    String? managerId,
    List<String>? assignedWorkers,
    DateTime? createdAt,
    DateTime? updatedAt,
    PropertyMetrics? metrics,
    Map<String, dynamic>? configuration,
  }) {
    return PropertyModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      type: type ?? this.type,
      status: status ?? this.status,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      description: description ?? this.description,
      images: images ?? this.images,
      managerId: managerId ?? this.managerId,
      assignedWorkers: assignedWorkers ?? this.assignedWorkers,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metrics: metrics ?? this.metrics,
      configuration: configuration ?? this.configuration,
    );
  }

  String get statusDisplayName {
    switch (status) {
      case PropertyStatus.active:
        return 'Active';
      case PropertyStatus.inactive:
        return 'Inactive';
      case PropertyStatus.maintenance:
        return 'Under Maintenance';
      case PropertyStatus.critical:
        return 'Critical';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case PropertyType.residential:
        return 'Residential';
      case PropertyType.commercial:
        return 'Commercial';
      case PropertyType.industrial:
        return 'Industrial';
      case PropertyType.mixed:
        return 'Mixed Use';
    }
  }

  bool get hasLocation => latitude != null && longitude != null;

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        type,
        status,
        latitude,
        longitude,
        description,
        images,
        managerId,
        assignedWorkers,
        createdAt,
        updatedAt,
        metrics,
        configuration,
      ];
}

@JsonSerializable()
class PropertyMetrics extends Equatable {
  final double? fuelLevel;
  final int? activeCameras;
  final int? totalCameras;
  final int? openMaintenanceIssues;
  final int? totalMaintenanceIssues;
  final DateTime? lastVisit;
  final int? ottServicesActive;
  final int? ottServicesTotal;
  final double? powerStatus;
  final DateTime? lastUpdated;

  const PropertyMetrics({
    this.fuelLevel,
    this.activeCameras,
    this.totalCameras,
    this.openMaintenanceIssues,
    this.totalMaintenanceIssues,
    this.lastVisit,
    this.ottServicesActive,
    this.ottServicesTotal,
    this.powerStatus,
    this.lastUpdated,
  });

  factory PropertyMetrics.fromJson(Map<String, dynamic> json) =>
      _$PropertyMetricsFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyMetricsToJson(this);

  PropertyMetrics copyWith({
    double? fuelLevel,
    int? activeCameras,
    int? totalCameras,
    int? openMaintenanceIssues,
    int? totalMaintenanceIssues,
    DateTime? lastVisit,
    int? ottServicesActive,
    int? ottServicesTotal,
    double? powerStatus,
    DateTime? lastUpdated,
  }) {
    return PropertyMetrics(
      fuelLevel: fuelLevel ?? this.fuelLevel,
      activeCameras: activeCameras ?? this.activeCameras,
      totalCameras: totalCameras ?? this.totalCameras,
      openMaintenanceIssues: openMaintenanceIssues ?? this.openMaintenanceIssues,
      totalMaintenanceIssues: totalMaintenanceIssues ?? this.totalMaintenanceIssues,
      lastVisit: lastVisit ?? this.lastVisit,
      ottServicesActive: ottServicesActive ?? this.ottServicesActive,
      ottServicesTotal: ottServicesTotal ?? this.ottServicesTotal,
      powerStatus: powerStatus ?? this.powerStatus,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  double get cameraHealthPercentage {
    if (totalCameras == null || totalCameras == 0) return 0.0;
    return ((activeCameras ?? 0) / totalCameras!) * 100;
  }

  double get ottHealthPercentage {
    if (ottServicesTotal == null || ottServicesTotal == 0) return 0.0;
    return ((ottServicesActive ?? 0) / ottServicesTotal!) * 100;
  }

  bool get isFuelLow => (fuelLevel ?? 100) < 25;
  bool get isFuelCritical => (fuelLevel ?? 100) < 10;

  @override
  List<Object?> get props => [
        fuelLevel,
        activeCameras,
        totalCameras,
        openMaintenanceIssues,
        totalMaintenanceIssues,
        lastVisit,
        ottServicesActive,
        ottServicesTotal,
        powerStatus,
        lastUpdated,
      ];
}
