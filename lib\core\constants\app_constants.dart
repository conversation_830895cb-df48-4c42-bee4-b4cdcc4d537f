class AppConstants {
  // App Information
  static const String appName = 'Property Management';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://your-backend-api.com/api';
  static const String devBaseUrl = 'http://localhost:3000/api';
  static const String stagingBaseUrl = 'https://staging-api.com/api';
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Hive Box Names
  static const String userBox = 'user_box';
  static const String propertyBox = 'property_box';
  static const String fuelBox = 'fuel_box';
  static const String maintenanceBox = 'maintenance_box';
  static const String attendanceBox = 'attendance_box';
  static const String settingsBox = 'settings_box';
  
  // API Timeouts
  static const int connectTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int imageQuality = 80;
  static const double maxImageWidth = 1920;
  static const double maxImageHeight = 1080;
  
  // Fuel Thresholds
  static const double lowFuelThreshold = 25.0; // 25%
  static const double criticalFuelThreshold = 10.0; // 10%
  
  // Notification Channels
  static const String generalNotificationChannel = 'general';
  static const String alertNotificationChannel = 'alerts';
  static const String maintenanceNotificationChannel = 'maintenance';
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-ddTHH:mm:ss.SSSZ';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 500;
  
  // Sync Configuration
  static const int syncInterval = 300; // 5 minutes in seconds
  static const int maxRetryAttempts = 3;
  static const int retryDelay = 2; // seconds
}
