import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../core/theme/app_theme.dart';
import '../../data/models/property_model.dart';

class PropertyCard extends StatelessWidget {
  final PropertyModel property;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool isFavorite;
  final bool showMetrics;

  const PropertyCard({
    super.key,
    required this.property,
    this.onTap,
    this.onFavorite,
    this.isFavorite = false,
    this.showMetrics = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Image
            _buildPropertyImage(),
            
            // Property Details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with name and status
                  _buildHeader(),
                  
                  const SizedBox(height: 8),
                  
                  // Address
                  _buildAddress(),
                  
                  const SizedBox(height: 12),
                  
                  // Type and Status badges
                  _buildBadges(),
                  
                  if (showMetrics && property.metrics != null) ...[
                    const SizedBox(height: 12),
                    _buildMetrics(),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyImage() {
    return Container(
      height: 160,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        color: AppTheme.backgroundColor,
      ),
      child: Stack(
        children: [
          // Image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            child: property.images?.isNotEmpty == true
                ? CachedNetworkImage(
                    imageUrl: property.images!.first,
                    width: double.infinity,
                    height: 160,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppTheme.backgroundColor,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => _buildPlaceholderImage(),
                  )
                : _buildPlaceholderImage(),
          ),
          
          // Status overlay
          Positioned(
            top: 12,
            left: 12,
            child: _buildStatusChip(),
          ),
          
          // Favorite button
          if (onFavorite != null)
            Positioned(
              top: 12,
              right: 12,
              child: _buildFavoriteButton(),
            ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: 160,
      color: AppTheme.backgroundColor,
      child: Icon(
        Icons.business,
        size: 48,
        color: AppTheme.textSecondaryColor,
      ),
    );
  }

  Widget _buildStatusChip() {
    Color statusColor;
    switch (property.status) {
      case PropertyStatus.active:
        statusColor = AppTheme.successColor;
        break;
      case PropertyStatus.inactive:
        statusColor = AppTheme.textSecondaryColor;
        break;
      case PropertyStatus.maintenance:
        statusColor = AppTheme.warningColor;
        break;
      case PropertyStatus.critical:
        statusColor = AppTheme.errorColor;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        property.statusDisplayName,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildFavoriteButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(
          isFavorite ? Icons.favorite : Icons.favorite_border,
          color: isFavorite ? AppTheme.errorColor : AppTheme.textSecondaryColor,
        ),
        onPressed: onFavorite,
        iconSize: 20,
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(
          minWidth: 36,
          minHeight: 36,
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Text(
            property.name,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (property.metrics?.lastVisit != null)
          Text(
            _formatLastVisit(property.metrics!.lastVisit!),
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
          ),
      ],
    );
  }

  Widget _buildAddress() {
    return Row(
      children: [
        const Icon(
          Icons.location_on_outlined,
          size: 16,
          color: AppTheme.textSecondaryColor,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            property.address,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildBadges() {
    return Row(
      children: [
        _buildTypeBadge(),
        const SizedBox(width: 8),
        if (property.assignedWorkers?.isNotEmpty == true)
          _buildWorkersBadge(),
      ],
    );
  }

  Widget _buildTypeBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Text(
        property.typeDisplayName,
        style: const TextStyle(
          fontSize: 12,
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildWorkersBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.secondaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.secondaryColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.people_outline,
            size: 12,
            color: AppTheme.secondaryColor,
          ),
          const SizedBox(width: 4),
          Text(
            '${property.assignedWorkers!.length}',
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.secondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetrics() {
    final metrics = property.metrics!;
    return Row(
      children: [
        if (metrics.fuelLevel != null)
          Expanded(
            child: _buildMetricItem(
              icon: Icons.local_gas_station,
              label: 'Fuel',
              value: '${metrics.fuelLevel!.toInt()}%',
              color: _getFuelColor(metrics.fuelLevel!),
            ),
          ),
        if (metrics.activeCameras != null && metrics.totalCameras != null)
          Expanded(
            child: _buildMetricItem(
              icon: Icons.videocam,
              label: 'Cameras',
              value: '${metrics.activeCameras}/${metrics.totalCameras}',
              color: metrics.cameraHealthPercentage > 80
                  ? AppTheme.successColor
                  : AppTheme.warningColor,
            ),
          ),
        if (metrics.openMaintenanceIssues != null)
          Expanded(
            child: _buildMetricItem(
              icon: Icons.build,
              label: 'Issues',
              value: '${metrics.openMaintenanceIssues}',
              color: metrics.openMaintenanceIssues! > 0
                  ? AppTheme.errorColor
                  : AppTheme.successColor,
            ),
          ),
      ],
    );
  }

  Widget _buildMetricItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Color _getFuelColor(double fuelLevel) {
    if (fuelLevel < 10) return AppTheme.errorColor;
    if (fuelLevel < 25) return AppTheme.warningColor;
    return AppTheme.successColor;
  }

  String _formatLastVisit(DateTime lastVisit) {
    final now = DateTime.now();
    final difference = now.difference(lastVisit);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
