import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../../core/utils/logger.dart';
import '../../../data/models/property_model.dart';
import '../../../data/services/api_service.dart';
import '../../../data/services/storage_service.dart';
import 'property_event.dart';
import 'property_state.dart';

class PropertyBloc extends Bloc<PropertyEvent, PropertyState> {
  final ApiService _apiService;
  final StorageService _storageService;
  final Connectivity _connectivity;

  PropertyBloc({
    required ApiService apiService,
    required StorageService storageService,
    Connectivity? connectivity,
  })  : _apiService = apiService,
        _storageService = storageService,
        _connectivity = connectivity ?? Connectivity(),
        super(const PropertyInitial()) {
    on<PropertyLoadRequested>(_onPropertyLoadRequested);
    on<PropertySearchRequested>(_onPropertySearchRequested);
    on<PropertyFilterChanged>(_onPropertyFilterChanged);
    on<PropertyDetailsRequested>(_onPropertyDetailsRequested);
    on<PropertyStatusRequested>(_onPropertyStatusRequested);
    on<PropertyUpdateRequested>(_onPropertyUpdateRequested);
    on<PropertyCreateRequested>(_onPropertyCreateRequested);
    on<PropertyDeleteRequested>(_onPropertyDeleteRequested);
    on<PropertyRefreshRequested>(_onPropertyRefreshRequested);
    on<PropertyCacheCleared>(_onPropertyCacheCleared);
    on<PropertySyncRequested>(_onPropertySyncRequested);
    on<PropertyOfflineDataLoaded>(_onPropertyOfflineDataLoaded);
  }

  Future<void> _onPropertyLoadRequested(
    PropertyLoadRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyLoadRequested', 'Loading properties');
      
      if (state.properties.isEmpty || event.forceRefresh) {
        emit(const PropertyLoading());
      }

      // Check connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      List<PropertyModel> properties = [];

      if (isOnline) {
        try {
          // Fetch from API
          properties = await _apiService.getProperties();
          
          // Cache the data
          await _storageService.saveProperties(properties);
          
          AppLogger.info('Properties loaded from API: ${properties.length}');
        } catch (e) {
          AppLogger.error('Failed to load properties from API', e);
          
          // Fallback to cached data
          properties = _storageService.getProperties();
          AppLogger.info('Loaded properties from cache: ${properties.length}');
        }
      } else {
        // Load from cache when offline
        properties = _storageService.getProperties();
        AppLogger.info('Loaded properties from cache (offline): ${properties.length}');
      }

      emit(PropertyLoaded(
        properties: properties,
        isOffline: !isOnline,
      ));
    } catch (e) {
      AppLogger.error('Property load failed', e);
      emit(PropertyError(
        errorMessage: 'Failed to load properties: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertySearchRequested(
    PropertySearchRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertySearchRequested', 'Searching: ${event.query}');
      
      emit(PropertySearching(
        properties: state.properties,
        searchQuery: event.query,
        statusFilter: state.statusFilter,
        typeFilter: state.typeFilter,
      ));

      // Perform local search
      final filteredProperties = _filterProperties(
        properties: state.properties,
        searchQuery: event.query,
        statusFilter: state.statusFilter,
        typeFilter: state.typeFilter,
      );

      emit(PropertyLoaded(
        properties: state.properties,
        filteredProperties: filteredProperties,
        searchQuery: event.query,
        statusFilter: state.statusFilter,
        typeFilter: state.typeFilter,
        isOffline: state.isOffline,
      ));
    } catch (e) {
      AppLogger.error('Property search failed', e);
      emit(PropertyError(
        errorMessage: 'Search failed: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertyFilterChanged(
    PropertyFilterChanged event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyFilterChanged', 
          'Status: ${event.status}, Type: ${event.type}');

      final filteredProperties = _filterProperties(
        properties: state.properties,
        searchQuery: state.searchQuery,
        statusFilter: event.status,
        typeFilter: event.type,
      );

      emit(PropertyLoaded(
        properties: state.properties,
        filteredProperties: filteredProperties,
        searchQuery: state.searchQuery,
        statusFilter: event.status,
        typeFilter: event.type,
        isOffline: state.isOffline,
      ));
    } catch (e) {
      AppLogger.error('Property filter failed', e);
      emit(PropertyError(
        errorMessage: 'Filter failed: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertyDetailsRequested(
    PropertyDetailsRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyDetailsRequested', event.propertyId);

      // First try to find in current state
      PropertyModel? property = state.properties
          .where((p) => p.id == event.propertyId)
          .firstOrNull;

      if (property != null) {
        emit(state.copyWith(selectedProperty: property));
      }

      // Check connectivity for fresh data
      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        try {
          // Fetch fresh data from API
          property = await _apiService.getProperty(event.propertyId);
          
          // Update cache
          await _storageService.saveProperty(property);
          
          // Update state with fresh data
          final updatedProperties = state.properties.map((p) {
            return p.id == property!.id ? property : p;
          }).toList();

          emit(state.copyWith(
            properties: updatedProperties,
            selectedProperty: property,
          ));
        } catch (e) {
          AppLogger.error('Failed to fetch property details from API', e);
          // Continue with cached data
        }
      }
    } catch (e) {
      AppLogger.error('Property details request failed', e);
      emit(PropertyError(
        errorMessage: 'Failed to load property details: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertyStatusRequested(
    PropertyStatusRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyStatusRequested', event.propertyId);

      final connectivityResult = await _connectivity.checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        try {
          final metrics = await _apiService.getPropertyStatus(event.propertyId);
          
          emit(state.copyWith(selectedPropertyMetrics: metrics));
          AppLogger.info('Property status loaded for ${event.propertyId}');
        } catch (e) {
          AppLogger.error('Failed to load property status', e);
        }
      }
    } catch (e) {
      AppLogger.error('Property status request failed', e);
    }
  }

  Future<void> _onPropertyUpdateRequested(
    PropertyUpdateRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyUpdateRequested', event.propertyId);
      
      emit(PropertyUpdating(
        properties: state.properties,
        selectedProperty: state.selectedProperty,
      ));

      final updatedProperty = await _apiService.updateProperty(
        event.propertyId,
        event.data,
      );

      // Update local storage
      await _storageService.saveProperty(updatedProperty);

      // Update state
      final updatedProperties = state.properties.map((p) {
        return p.id == updatedProperty.id ? updatedProperty : p;
      }).toList();

      emit(PropertyLoaded(
        properties: updatedProperties,
        filteredProperties: _filterProperties(
          properties: updatedProperties,
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          typeFilter: state.typeFilter,
        ),
        searchQuery: state.searchQuery,
        statusFilter: state.statusFilter,
        typeFilter: state.typeFilter,
        isOffline: state.isOffline,
      ));

      AppLogger.info('Property updated successfully: ${event.propertyId}');
    } catch (e) {
      AppLogger.error('Property update failed', e);
      emit(PropertyError(
        errorMessage: 'Failed to update property: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertyCreateRequested(
    PropertyCreateRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyCreateRequested', 'Creating property');
      
      emit(PropertyCreating(properties: state.properties));

      // For now, we'll assume the API service has a create method
      // final newProperty = await _apiService.createProperty(event.data);

      // Simulate property creation for demo
      final newProperty = PropertyModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: event.data['name'] ?? 'New Property',
        address: event.data['address'] ?? 'Address',
        type: PropertyType.commercial,
        status: PropertyStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Update local storage
      await _storageService.saveProperty(newProperty);

      // Update state
      final updatedProperties = [...state.properties, newProperty];

      emit(PropertyLoaded(
        properties: updatedProperties,
        filteredProperties: _filterProperties(
          properties: updatedProperties,
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          typeFilter: state.typeFilter,
        ),
        searchQuery: state.searchQuery,
        statusFilter: state.statusFilter,
        typeFilter: state.typeFilter,
        isOffline: state.isOffline,
      ));

      AppLogger.info('Property created successfully');
    } catch (e) {
      AppLogger.error('Property creation failed', e);
      emit(PropertyError(
        errorMessage: 'Failed to create property: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertyDeleteRequested(
    PropertyDeleteRequested event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      AppLogger.stateChange('PropertyBloc', 'PropertyDeleteRequested', event.propertyId);
      
      emit(PropertyDeleting(
        properties: state.properties,
        propertyId: event.propertyId,
      ));

      // Delete from API (when implemented)
      // await _apiService.deleteProperty(event.propertyId);

      // Delete from local storage
      await _storageService.deleteProperty(event.propertyId);

      // Update state
      final updatedProperties = state.properties
          .where((p) => p.id != event.propertyId)
          .toList();

      emit(PropertyLoaded(
        properties: updatedProperties,
        filteredProperties: _filterProperties(
          properties: updatedProperties,
          searchQuery: state.searchQuery,
          statusFilter: state.statusFilter,
          typeFilter: state.typeFilter,
        ),
        searchQuery: state.searchQuery,
        statusFilter: state.statusFilter,
        typeFilter: state.typeFilter,
        isOffline: state.isOffline,
      ));

      AppLogger.info('Property deleted successfully: ${event.propertyId}');
    } catch (e) {
      AppLogger.error('Property deletion failed', e);
      emit(PropertyError(
        errorMessage: 'Failed to delete property: ${e.toString()}',
        properties: state.properties,
      ));
    }
  }

  Future<void> _onPropertyRefreshRequested(
    PropertyRefreshRequested event,
    Emitter<PropertyState> emit,
  ) async {
    add(const PropertyLoadRequested(forceRefresh: true));
  }

  Future<void> _onPropertyCacheCleared(
    PropertyCacheCleared event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      await _storageService.clearProperties();
      emit(const PropertyInitial());
      AppLogger.info('Property cache cleared');
    } catch (e) {
      AppLogger.error('Failed to clear property cache', e);
    }
  }

  Future<void> _onPropertySyncRequested(
    PropertySyncRequested event,
    Emitter<PropertyState> emit,
  ) async {
    add(const PropertyLoadRequested(forceRefresh: true));
  }

  Future<void> _onPropertyOfflineDataLoaded(
    PropertyOfflineDataLoaded event,
    Emitter<PropertyState> emit,
  ) async {
    try {
      final properties = _storageService.getProperties();
      emit(PropertyLoaded(
        properties: properties,
        isOffline: true,
      ));
      AppLogger.info('Offline property data loaded: ${properties.length}');
    } catch (e) {
      AppLogger.error('Failed to load offline property data', e);
      emit(PropertyError(
        errorMessage: 'Failed to load offline data: ${e.toString()}',
        isOffline: true,
      ));
    }
  }

  List<PropertyModel> _filterProperties({
    required List<PropertyModel> properties,
    String? searchQuery,
    String? statusFilter,
    String? typeFilter,
  }) {
    var filtered = properties;

    // Apply search filter
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filtered = filtered.where((property) {
        return property.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
               property.address.toLowerCase().contains(searchQuery.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (statusFilter != null && statusFilter.isNotEmpty) {
      filtered = filtered.where((property) {
        return property.status.toString().split('.').last == statusFilter;
      }).toList();
    }

    // Apply type filter
    if (typeFilter != null && typeFilter.isNotEmpty) {
      filtered = filtered.where((property) {
        return property.type.toString().split('.').last == typeFilter;
      }).toList();
    }

    return filtered;
  }
}
