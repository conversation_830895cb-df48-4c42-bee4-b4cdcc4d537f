import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'fuel_model.g.dart';

enum FuelEntryType {
  @JsonValue('reading')
  reading,
  @JsonValue('addition')
  addition,
  @JsonValue('consumption')
  consumption,
}

@JsonSerializable()
class FuelModel extends Equatable {
  final String id;
  final String propertyId;
  final double currentLevel;
  final double? previousLevel;
  final FuelEntryType entryType;
  final double? addedAmount;
  final String? notes;
  final List<String>? images;
  final String recordedBy;
  final DateTime recordedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const FuelModel({
    required this.id,
    required this.propertyId,
    required this.currentLevel,
    this.previousLevel,
    required this.entryType,
    this.addedAmount,
    this.notes,
    this.images,
    required this.recordedBy,
    required this.recordedAt,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory FuelModel.fromJson(Map<String, dynamic> json) =>
      _$FuelModelFromJson(json);

  Map<String, dynamic> toJson() => _$FuelModelToJson(this);

  FuelModel copyWith({
    String? id,
    String? propertyId,
    double? currentLevel,
    double? previousLevel,
    FuelEntryType? entryType,
    double? addedAmount,
    String? notes,
    List<String>? images,
    String? recordedBy,
    DateTime? recordedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return FuelModel(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      currentLevel: currentLevel ?? this.currentLevel,
      previousLevel: previousLevel ?? this.previousLevel,
      entryType: entryType ?? this.entryType,
      addedAmount: addedAmount ?? this.addedAmount,
      notes: notes ?? this.notes,
      images: images ?? this.images,
      recordedBy: recordedBy ?? this.recordedBy,
      recordedAt: recordedAt ?? this.recordedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  String get entryTypeDisplayName {
    switch (entryType) {
      case FuelEntryType.reading:
        return 'Fuel Reading';
      case FuelEntryType.addition:
        return 'Fuel Addition';
      case FuelEntryType.consumption:
        return 'Fuel Consumption';
    }
  }

  double? get consumedAmount {
    if (previousLevel != null && entryType == FuelEntryType.consumption) {
      return previousLevel! - currentLevel;
    }
    return null;
  }

  bool get isLowFuel => currentLevel < 25.0;
  bool get isCriticalFuel => currentLevel < 10.0;

  String get fuelStatusText {
    if (isCriticalFuel) return 'Critical';
    if (isLowFuel) return 'Low';
    return 'Normal';
  }

  @override
  List<Object?> get props => [
        id,
        propertyId,
        currentLevel,
        previousLevel,
        entryType,
        addedAmount,
        notes,
        images,
        recordedBy,
        recordedAt,
        createdAt,
        updatedAt,
        metadata,
      ];
}

@JsonSerializable()
class FuelCreateRequest extends Equatable {
  final String propertyId;
  final double currentLevel;
  final FuelEntryType entryType;
  final double? addedAmount;
  final String? notes;
  final List<String>? imageBase64;

  const FuelCreateRequest({
    required this.propertyId,
    required this.currentLevel,
    required this.entryType,
    this.addedAmount,
    this.notes,
    this.imageBase64,
  });

  factory FuelCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$FuelCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FuelCreateRequestToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        currentLevel,
        entryType,
        addedAmount,
        notes,
        imageBase64,
      ];
}

@JsonSerializable()
class FuelAnalytics extends Equatable {
  final String propertyId;
  final double averageConsumption;
  final double totalConsumption;
  final double totalAdditions;
  final int totalReadings;
  final DateTime periodStart;
  final DateTime periodEnd;
  final List<FuelTrend>? trends;

  const FuelAnalytics({
    required this.propertyId,
    required this.averageConsumption,
    required this.totalConsumption,
    required this.totalAdditions,
    required this.totalReadings,
    required this.periodStart,
    required this.periodEnd,
    this.trends,
  });

  factory FuelAnalytics.fromJson(Map<String, dynamic> json) =>
      _$FuelAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$FuelAnalyticsToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        averageConsumption,
        totalConsumption,
        totalAdditions,
        totalReadings,
        periodStart,
        periodEnd,
        trends,
      ];
}

@JsonSerializable()
class FuelTrend extends Equatable {
  final DateTime date;
  final double level;
  final double? consumption;
  final double? addition;

  const FuelTrend({
    required this.date,
    required this.level,
    this.consumption,
    this.addition,
  });

  factory FuelTrend.fromJson(Map<String, dynamic> json) =>
      _$FuelTrendFromJson(json);

  Map<String, dynamic> toJson() => _$FuelTrendToJson(this);

  @override
  List<Object?> get props => [date, level, consumption, addition];
}
