import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../core/utils/logger.dart';
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_state.dart';
import 'screens/auth/login_screen.dart';
import 'screens/splash/splash_screen.dart';
import 'screens/dashboard/dashboard_screen.dart';
import 'screens/onboarding/onboarding_screen.dart';

class AppWrapper extends StatelessWidget {
  const AppWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        // Handle auth state changes that require side effects
        if (state.hasError) {
          AppLogger.error('Auth error: ${state.errorMessage}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      builder: (context, state) {
        AppLogger.stateChange('AuthBloc', 'StateChange', state.status.toString());
        
        switch (state.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            return const SplashScreen();
            
          case AuthStatus.authenticated:
            if (state.user != null) {
              AppLogger.navigation('AppWrapper', 'Dashboard');
              return const DashboardScreen();
            }
            return const LoginScreen();
            
          case AuthStatus.unauthenticated:
            if (state.isFirstTime) {
              AppLogger.navigation('AppWrapper', 'Onboarding');
              return const OnboardingScreen();
            }
            AppLogger.navigation('AppWrapper', 'Login');
            return const LoginScreen();
            
          case AuthStatus.error:
            // Show error state but allow retry
            return const LoginScreen();
        }
      },
    );
  }
}

class AppNavigator {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  static BuildContext? get context => navigatorKey.currentContext;
  
  static void pushReplacement(Widget screen) {
    if (context != null) {
      Navigator.of(context!).pushReplacement(
        MaterialPageRoute(builder: (_) => screen),
      );
    }
  }
  
  static void push(Widget screen) {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(builder: (_) => screen),
      );
    }
  }
  
  static void pop() {
    if (context != null && Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop();
    }
  }
  
  static void popUntil(String routeName) {
    if (context != null) {
      Navigator.of(context!).popUntil(ModalRoute.withName(routeName));
    }
  }
  
  static void pushAndClearStack(Widget screen) {
    if (context != null) {
      Navigator.of(context!).pushAndRemoveUntil(
        MaterialPageRoute(builder: (_) => screen),
        (route) => false,
      );
    }
  }
}
