// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PropertyModel _$PropertyModelFromJson(Map<String, dynamic> json) =>
    PropertyModel(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      type: $enumDecode(_$PropertyTypeEnumMap, json['type']),
      status: $enumDecode(_$PropertyStatusEnumMap, json['status']),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      description: json['description'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      managerId: json['managerId'] as String?,
      assignedWorkers: (json['assignedWorkers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      metrics: json['metrics'] == null
          ? null
          : PropertyMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
      configuration: json['configuration'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PropertyModelToJson(PropertyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'type': _$PropertyTypeEnumMap[instance.type]!,
      'status': _$PropertyStatusEnumMap[instance.status]!,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'description': instance.description,
      'images': instance.images,
      'managerId': instance.managerId,
      'assignedWorkers': instance.assignedWorkers,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'metrics': instance.metrics,
      'configuration': instance.configuration,
    };

const _$PropertyTypeEnumMap = {
  PropertyType.residential: 'residential',
  PropertyType.commercial: 'commercial',
  PropertyType.industrial: 'industrial',
  PropertyType.mixed: 'mixed',
};

const _$PropertyStatusEnumMap = {
  PropertyStatus.active: 'active',
  PropertyStatus.inactive: 'inactive',
  PropertyStatus.maintenance: 'maintenance',
  PropertyStatus.critical: 'critical',
};

PropertyMetrics _$PropertyMetricsFromJson(Map<String, dynamic> json) =>
    PropertyMetrics(
      fuelLevel: (json['fuelLevel'] as num?)?.toDouble(),
      activeCameras: (json['activeCameras'] as num?)?.toInt(),
      totalCameras: (json['totalCameras'] as num?)?.toInt(),
      openMaintenanceIssues: (json['openMaintenanceIssues'] as num?)?.toInt(),
      totalMaintenanceIssues: (json['totalMaintenanceIssues'] as num?)?.toInt(),
      lastVisit: json['lastVisit'] == null
          ? null
          : DateTime.parse(json['lastVisit'] as String),
      ottServicesActive: (json['ottServicesActive'] as num?)?.toInt(),
      ottServicesTotal: (json['ottServicesTotal'] as num?)?.toInt(),
      powerStatus: (json['powerStatus'] as num?)?.toDouble(),
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$PropertyMetricsToJson(PropertyMetrics instance) =>
    <String, dynamic>{
      'fuelLevel': instance.fuelLevel,
      'activeCameras': instance.activeCameras,
      'totalCameras': instance.totalCameras,
      'openMaintenanceIssues': instance.openMaintenanceIssues,
      'totalMaintenanceIssues': instance.totalMaintenanceIssues,
      'lastVisit': instance.lastVisit?.toIso8601String(),
      'ottServicesActive': instance.ottServicesActive,
      'ottServicesTotal': instance.ottServicesTotal,
      'powerStatus': instance.powerStatus,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };
